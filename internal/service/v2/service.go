package v2

import (
	"context"

	"go-common/library/conf/paladin.v2"
	"go-common/library/database/sql"
	"go-common/library/log"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/google/wire"

	pb "recommend-base/api/v2"
	"recommend-base/internal/dao"
	"recommend-base/internal/model/mrecommendedexposurelevel"
)

var Provider = wire.NewSet(
	New,
	wire.Bind(new(pb.LiveRoomServer), new(*Service)),
	wire.Bind(new(pb.RecommendServer), new(*Service)),
)

// Service v2 service.
type Service struct {
	ac  *paladin.Map
	dao dao.Dao
	db  *sql.DB
}

// New new a v2 service and return.
func New(d dao.Dao, db *sql.DB) (s *Service, cf func(), err error) {
	s = &Service{
		ac:  &paladin.TOML{},
		dao: d,
		db:  db,
	}
	cf = s.Close
	err = paladin.Watch("application.toml", s.ac)
	return
}

// Ping ping the resource.
func (s *Service) Ping(ctx context.Context, e *empty.Empty) (*empty.Empty, error) {
	return &empty.Empty{}, s.dao.Ping(ctx)
}

// Close close the resource.
func (s *Service) Close() {
}

// GetAllLivingRoomsInfo 获取全部直播间信息
func (s *Service) GetAllLivingRoomsInfo(ctx context.Context, req *pb.LivingRoomListReq) (resp *pb.AllLivingRoomsInfoResp, err error) {
	roomsInfo, err := s.dao.LivingRoomsInfo(ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}
	if roomsInfo == nil || len(roomsInfo.List) == 0 {
		return &pb.AllLivingRoomsInfoResp{
			List: make([]*pb.AllLivingRoomInfo, 0),
		}, nil
	}

	roomIDs := make([]int64, 0, len(roomsInfo.List))
	for _, roomInfo := range roomsInfo.List {
		roomIDs = append(roomIDs, roomInfo.RoomID)
	}

	// 获取房间额外信息
	roomsExtraInfo, err := s.dao.RoomsExtraInfo(ctx, dao.RoomsExtraInfoParams{RoomIDs: roomIDs})
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp = new(pb.AllLivingRoomsInfoResp)
	resp.List = make([]*pb.AllLivingRoomInfo, 0, len(roomsInfo.List))
	for _, info := range roomsInfo.List {
		item := &pb.AllLivingRoomInfo{
			Roomid:        info.RoomID,
			Uid:           info.UserID,
			RoomTitle:     info.RoomTitle,
			LiveStartTime: info.LiveStartTime,
			Ctime:         info.CreateTime,
			Score:         info.Score,
		}

		for _, extraInfo := range roomsExtraInfo.List {
			if extraInfo.RoomID == info.RoomID {
				item.CatalogId = extraInfo.CatalogID
				item.SubCatalogId = extraInfo.SubCatalogID
				item.CustomTagId = extraInfo.CustomTagID
				item.CustomTagName = extraInfo.CustomTagName
				item.Online = extraInfo.Online
				item.ActuallyOnline = extraInfo.ActuallyOnline
				item.MessageCount = extraInfo.MessageCount
				item.Board = extraInfo.Board
				item.IsNewStar = extraInfo.IsNewStar
				break
			}
		}
		resp.List = append(resp.List, item)
	}

	// 设置分页信息
	resp.Pagination = &pb.Pagination{
		Maxpage:  roomsInfo.Pagination.MaxPage,
		Count:    roomsInfo.Pagination.Count,
		P:        roomsInfo.Pagination.P,
		Pagesize: roomsInfo.Pagination.PageSize,
	}

	return resp, nil
}

// GetHomeFeedInterventionCards 获取首页 Feed 干预卡列表
func (s *Service) GetHomeFeedInterventionCards(ctx context.Context, req *pb.HomeFeedInterventionCardsReq) (resp *pb.InterventionCardsResp, err error) {
	levels, err := s.dao.GetRecommendedExposureLevels(ctx)
	if err != nil {
		return nil, err
	}
	exposureLevelMap := make(map[int64]int64)
	for _, level := range levels {
		if level.Scene != mrecommendedexposurelevel.SceneHomePage {
			continue
		}
		exposureLevelMap[level.ID] = level.Exposure
	}

	cards, err := s.dao.GetHomeFeedInterventionCardsWithUnion(ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	list := make([]*pb.InterventionCard, 0, len(cards.List))
	for _, card := range cards.List {
		exposureTarget, ok := exposureLevelMap[card.ExposureLevelID]
		if !ok {
			// 对于运营推荐卡，如果没有找到对应的曝光等级，使用默认值 0
			if card.OperateSource == dao.InterventionCardOperateSourceOperatorRecommend {
				exposureTarget = 0
			} else {
				log.Error("GetHomeFeedInterventionCards.exposureLevelMap unknown exposure level, card: %+v, levels: %+v", card, levels)
				continue
			}
		}
		list = append(list, &pb.InterventionCard{
			CardId:          card.CardID,
			CardType:        card.CardType,
			ElementId:       card.ElementID,
			StartTime:       card.StartTime,
			EndTime:         card.EndTime,
			ExposureTarget:  exposureTarget,
			ExposureCurrent: card.ExposureCurrent,
			CatalogId:       card.CatalogID,
			OperateSource:   card.OperateSource,
		})
	}

	resp = &pb.InterventionCardsResp{
		List: list,
		Pagination: &pb.Pagination{
			Maxpage:  cards.Pagination.MaxPage,
			Count:    cards.Pagination.Count,
			P:        cards.Pagination.P,
			Pagesize: cards.Pagination.PageSize,
		},
	}
	return resp, nil
}

// GetLivePageInterventionCards 获取直播页干预卡列表
func (s *Service) GetLivePageInterventionCards(ctx context.Context, req *pb.LivePageInterventionCardsReq) (resp *pb.InterventionCardsResp, err error) {
	levels, err := s.dao.GetRecommendedExposureLevels(ctx)
	if err != nil {
		return nil, err
	}
	exposureLevelMap := make(map[int64]int64)
	for _, level := range levels {
		if level.Scene != mrecommendedexposurelevel.SceneLivePage {
			continue
		}
		exposureLevelMap[level.ID] = level.Exposure
	}
	cards, err := s.dao.GetLivePageInterventionCards(ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}
	list := make([]*pb.InterventionCard, 0, len(cards.List))
	for _, card := range cards.List {
		exposureTarget, ok := exposureLevelMap[card.ExposureLevelID]
		if !ok {
			log.Error("GetLivePageInterventionCards.exposureLevelMap unknown exposure level, card: %+v, levels: %+v", card, levels)
			continue
		}
		list = append(list, &pb.InterventionCard{
			CardId:          card.CardID,
			CardType:        card.CardType,
			ElementId:       card.ElementID,
			StartTime:       card.StartTime,
			EndTime:         card.EndTime,
			ExposureTarget:  exposureTarget,
			ExposureCurrent: card.ExposureCurrent,
			CatalogId:       card.CatalogID,
			OperateSource:   card.OperateSource,
		})
	}
	resp = &pb.InterventionCardsResp{
		List: list,
		Pagination: &pb.Pagination{
			Maxpage:  cards.Pagination.MaxPage,
			Count:    cards.Pagination.Count,
			P:        cards.Pagination.P,
			Pagesize: cards.Pagination.PageSize,
		},
	}
	return resp, nil
}
