// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: recommend-base/api/v2/api.proto

// package 命名使用 {appid}.{version} 的方式, version 形如 v1, v2 ..

package v2

import (
	context "context"
	fmt "fmt"
	io "io"
	math "math"
	math_bits "math/bits"

	git_bilibili_co_go_kratos_gogo_protobuf_compatible "git.bilibili.co/go-kratos/gogo-protobuf/compatible"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	empty "github.com/golang/protobuf/ptypes/empty"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type LivingRoomListReq struct {
	Page                 int64    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty" form:"page" validate:"required,min=1"`
	PageSize             int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty" form:"page_size" validate:"required,min=1,max=100"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LivingRoomListReq) Reset()         { *m = LivingRoomListReq{} }
func (m *LivingRoomListReq) String() string { return proto.CompactTextString(m) }
func (*LivingRoomListReq) ProtoMessage()    {}
func (*LivingRoomListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{0}
}
func (m *LivingRoomListReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LivingRoomListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LivingRoomListReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LivingRoomListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LivingRoomListReq.Merge(m, src)
}
func (m *LivingRoomListReq) XXX_Size() int {
	return m.Size()
}
func (m *LivingRoomListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LivingRoomListReq.DiscardUnknown(m)
}

var xxx_messageInfo_LivingRoomListReq proto.InternalMessageInfo

// 全量在播房间列表响应
type AllLivingRoomsInfoResp struct {
	// 房间信息数组
	List []*AllLivingRoomInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 分页信息
	Pagination           *Pagination `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AllLivingRoomsInfoResp) Reset()         { *m = AllLivingRoomsInfoResp{} }
func (m *AllLivingRoomsInfoResp) String() string { return proto.CompactTextString(m) }
func (*AllLivingRoomsInfoResp) ProtoMessage()    {}
func (*AllLivingRoomsInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{1}
}
func (m *AllLivingRoomsInfoResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllLivingRoomsInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AllLivingRoomsInfoResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AllLivingRoomsInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllLivingRoomsInfoResp.Merge(m, src)
}
func (m *AllLivingRoomsInfoResp) XXX_Size() int {
	return m.Size()
}
func (m *AllLivingRoomsInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AllLivingRoomsInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AllLivingRoomsInfoResp proto.InternalMessageInfo

type Pagination struct {
	Maxpage              int64    `protobuf:"varint,1,opt,name=maxpage,proto3" json:"maxpage,omitempty"`
	Count                int64    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	P                    int64    `protobuf:"varint,3,opt,name=p,proto3" json:"p,omitempty"`
	Pagesize             int64    `protobuf:"varint,4,opt,name=pagesize,proto3" json:"pagesize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Pagination) Reset()         { *m = Pagination{} }
func (m *Pagination) String() string { return proto.CompactTextString(m) }
func (*Pagination) ProtoMessage()    {}
func (*Pagination) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{2}
}
func (m *Pagination) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Pagination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Pagination.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Pagination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Pagination.Merge(m, src)
}
func (m *Pagination) XXX_Size() int {
	return m.Size()
}
func (m *Pagination) XXX_DiscardUnknown() {
	xxx_messageInfo_Pagination.DiscardUnknown(m)
}

var xxx_messageInfo_Pagination proto.InternalMessageInfo

type AllLivingRoomInfo struct {
	// 房间 id
	Roomid int64 `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	// 主播 id
	Uid int64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// 直播间一级分区 id
	CatalogId int64 `protobuf:"varint,3,opt,name=catalog_id,json=catalogId,proto3" json:"catalog_id,omitempty"`
	// 直播间二级分区 id
	SubCatalogId int64 `protobuf:"varint,4,opt,name=sub_catalog_id,json=subCatalogId,proto3" json:"sub_catalog_id,omitempty"`
	// 直播间挂载的个性词条 id
	CustomTagId int64 `protobuf:"varint,5,opt,name=custom_tag_id,json=customTagId,proto3" json:"custom_tag_id,omitempty"`
	// 直播间挂载的个性词条
	CustomTagName string `protobuf:"bytes,6,opt,name=custom_tag_name,json=customTagName,proto3" json:"custom_tag_name,omitempty"`
	// 直播间标题
	RoomTitle string `protobuf:"bytes,7,opt,name=room_title,json=roomTitle,proto3" json:"room_title,omitempty"`
	// 直播间创建时间（秒级时间戳）
	Ctime int64 `protobuf:"varint,8,opt,name=ctime,proto3" json:"ctime,omitempty"`
	// 本场直播开始时间（秒级时间戳）
	LiveStartTime int64 `protobuf:"varint,9,opt,name=live_start_time,json=liveStartTime,proto3" json:"live_start_time,omitempty"`
	// 实时在线人数（展示）
	Online int64 `protobuf:"varint,10,opt,name=online,proto3" json:"online,omitempty"`
	// 总进房
	ActuallyOnline int64 `protobuf:"varint,11,opt,name=actually_online,json=actuallyOnline,proto3" json:"actually_online,omitempty"`
	// 实时弹幕数（本场累积消息数）
	MessageCount int64 `protobuf:"varint,12,opt,name=message_count,json=messageCount,proto3" json:"message_count,omitempty"`
	// 实时热度
	Score int64 `protobuf:"varint,13,opt,name=score,proto3" json:"score,omitempty"`
	// 实时上榜人数（本场榜）
	Board int64 `protobuf:"varint,14,opt,name=board,proto3" json:"board,omitempty"`
	// 是否是新星
	IsNewStar            bool     `protobuf:"varint,15,opt,name=is_new_star,json=isNewStar,proto3" json:"is_new_star,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllLivingRoomInfo) Reset()         { *m = AllLivingRoomInfo{} }
func (m *AllLivingRoomInfo) String() string { return proto.CompactTextString(m) }
func (*AllLivingRoomInfo) ProtoMessage()    {}
func (*AllLivingRoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{3}
}
func (m *AllLivingRoomInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllLivingRoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AllLivingRoomInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AllLivingRoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllLivingRoomInfo.Merge(m, src)
}
func (m *AllLivingRoomInfo) XXX_Size() int {
	return m.Size()
}
func (m *AllLivingRoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AllLivingRoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AllLivingRoomInfo proto.InternalMessageInfo

// 首页 Feed 干预卡请求
type HomeFeedInterventionCardsReq struct {
	Page                 int64    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty" form:"page" validate:"required,min=1"`
	PageSize             int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty" form:"page_size" validate:"required,min=1,max=100"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HomeFeedInterventionCardsReq) Reset()         { *m = HomeFeedInterventionCardsReq{} }
func (m *HomeFeedInterventionCardsReq) String() string { return proto.CompactTextString(m) }
func (*HomeFeedInterventionCardsReq) ProtoMessage()    {}
func (*HomeFeedInterventionCardsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{4}
}
func (m *HomeFeedInterventionCardsReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *HomeFeedInterventionCardsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_HomeFeedInterventionCardsReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *HomeFeedInterventionCardsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomeFeedInterventionCardsReq.Merge(m, src)
}
func (m *HomeFeedInterventionCardsReq) XXX_Size() int {
	return m.Size()
}
func (m *HomeFeedInterventionCardsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HomeFeedInterventionCardsReq.DiscardUnknown(m)
}

var xxx_messageInfo_HomeFeedInterventionCardsReq proto.InternalMessageInfo

// 直播页干预卡请求
type LivePageInterventionCardsReq struct {
	Page                 int64    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty" form:"page" validate:"required,min=1"`
	PageSize             int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty" form:"page_size" validate:"required,min=1,max=100"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LivePageInterventionCardsReq) Reset()         { *m = LivePageInterventionCardsReq{} }
func (m *LivePageInterventionCardsReq) String() string { return proto.CompactTextString(m) }
func (*LivePageInterventionCardsReq) ProtoMessage()    {}
func (*LivePageInterventionCardsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{5}
}
func (m *LivePageInterventionCardsReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LivePageInterventionCardsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LivePageInterventionCardsReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LivePageInterventionCardsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LivePageInterventionCardsReq.Merge(m, src)
}
func (m *LivePageInterventionCardsReq) XXX_Size() int {
	return m.Size()
}
func (m *LivePageInterventionCardsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LivePageInterventionCardsReq.DiscardUnknown(m)
}

var xxx_messageInfo_LivePageInterventionCardsReq proto.InternalMessageInfo

// 干预卡列表响应
type InterventionCardsResp struct {
	// 卡片列表
	List []*InterventionCard `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 分页信息
	Pagination           *Pagination `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *InterventionCardsResp) Reset()         { *m = InterventionCardsResp{} }
func (m *InterventionCardsResp) String() string { return proto.CompactTextString(m) }
func (*InterventionCardsResp) ProtoMessage()    {}
func (*InterventionCardsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{6}
}
func (m *InterventionCardsResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InterventionCardsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InterventionCardsResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InterventionCardsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InterventionCardsResp.Merge(m, src)
}
func (m *InterventionCardsResp) XXX_Size() int {
	return m.Size()
}
func (m *InterventionCardsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InterventionCardsResp.DiscardUnknown(m)
}

var xxx_messageInfo_InterventionCardsResp proto.InternalMessageInfo

type InterventionCard struct {
	// 干预卡 ID
	CardId int64 `protobuf:"varint,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// 干预卡类型，1：剧集卡，2：直播卡
	CardType int64 `protobuf:"varint,2,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	// 干预卡元素 ID，例如剧集卡是剧集 ID、直播卡是直播间 ID
	ElementId int64 `protobuf:"varint,3,opt,name=element_id,json=elementId,proto3" json:"element_id,omitempty"`
	// 生效时间，秒级时间戳，包含此时间点（闭区间）
	StartTime int64 `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 失效时间，秒级时间戳，不包含此时间点（开区间）
	EndTime int64 `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 目标曝光量
	ExposureTarget int64 `protobuf:"varint,6,opt,name=exposure_target,json=exposureTarget,proto3" json:"exposure_target,omitempty"`
	// 当前曝光量
	ExposureCurrent int64 `protobuf:"varint,7,opt,name=exposure_current,json=exposureCurrent,proto3" json:"exposure_current,omitempty"`
	// 分区 id
	CatalogId int64 `protobuf:"varint,8,opt,name=catalog_id,json=catalogId,proto3" json:"catalog_id,omitempty"`
	// 干预来源，0：曝光流量卡，1：公会推荐位直播，2：热门列表固定主播，3：运营推荐主播，4：直播推荐排期
	OperateSource        int64    `protobuf:"varint,9,opt,name=operate_source,json=operateSource,proto3" json:"operate_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InterventionCard) Reset()         { *m = InterventionCard{} }
func (m *InterventionCard) String() string { return proto.CompactTextString(m) }
func (*InterventionCard) ProtoMessage()    {}
func (*InterventionCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_1f8d831bd7c20fb0, []int{7}
}
func (m *InterventionCard) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InterventionCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InterventionCard.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InterventionCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InterventionCard.Merge(m, src)
}
func (m *InterventionCard) XXX_Size() int {
	return m.Size()
}
func (m *InterventionCard) XXX_DiscardUnknown() {
	xxx_messageInfo_InterventionCard.DiscardUnknown(m)
}

var xxx_messageInfo_InterventionCard proto.InternalMessageInfo

func init() {
	proto.RegisterType((*LivingRoomListReq)(nil), "maoer.service.recommendbase.v2.LivingRoomListReq")
	proto.RegisterType((*AllLivingRoomsInfoResp)(nil), "maoer.service.recommendbase.v2.AllLivingRoomsInfoResp")
	proto.RegisterType((*Pagination)(nil), "maoer.service.recommendbase.v2.Pagination")
	proto.RegisterType((*AllLivingRoomInfo)(nil), "maoer.service.recommendbase.v2.AllLivingRoomInfo")
	proto.RegisterType((*HomeFeedInterventionCardsReq)(nil), "maoer.service.recommendbase.v2.HomeFeedInterventionCardsReq")
	proto.RegisterType((*LivePageInterventionCardsReq)(nil), "maoer.service.recommendbase.v2.LivePageInterventionCardsReq")
	proto.RegisterType((*InterventionCardsResp)(nil), "maoer.service.recommendbase.v2.InterventionCardsResp")
	proto.RegisterType((*InterventionCard)(nil), "maoer.service.recommendbase.v2.InterventionCard")
}

func init() { proto.RegisterFile("recommend-base/api/v2/api.proto", fileDescriptor_1f8d831bd7c20fb0) }

var fileDescriptor_1f8d831bd7c20fb0 = []byte{
	// 944 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x55, 0x41, 0x6f, 0xdc, 0x44,
	0x14, 0xae, 0xb3, 0x69, 0xb2, 0xfb, 0x92, 0xdd, 0xa4, 0xa3, 0x36, 0x98, 0x6d, 0xd9, 0xac, 0x0c,
	0xa5, 0x29, 0x6a, 0x9d, 0x64, 0x11, 0x39, 0x54, 0xe4, 0x40, 0x43, 0x29, 0x8b, 0xa2, 0x12, 0x39,
	0x7b, 0xe2, 0x62, 0xcd, 0xda, 0x2f, 0x66, 0x24, 0xdb, 0xe3, 0xce, 0x8c, 0xb7, 0x09, 0x47, 0xfe,
	0x02, 0x17, 0xfe, 0x01, 0x42, 0xdc, 0xb8, 0x70, 0xe0, 0x07, 0xd0, 0x23, 0x7f, 0x80, 0x0a, 0xc2,
	0x3f, 0xe8, 0x2f, 0x40, 0x33, 0xf6, 0x7a, 0xb7, 0x09, 0xc9, 0xaa, 0x48, 0x48, 0x70, 0x5a, 0xbf,
	0xef, 0x7d, 0xf3, 0xe6, 0xdb, 0x67, 0xbf, 0xef, 0xc1, 0xba, 0xc0, 0x80, 0x27, 0x09, 0xa6, 0xe1,
	0xfd, 0x21, 0x95, 0xb8, 0x49, 0x33, 0xb6, 0x39, 0xea, 0xe9, 0x1f, 0x37, 0x13, 0x5c, 0x71, 0xd2,
	0x49, 0x28, 0x47, 0xe1, 0x4a, 0x14, 0x23, 0x16, 0xa0, 0x5b, 0xd1, 0x35, 0xdb, 0x1d, 0xf5, 0xda,
	0xf7, 0x23, 0xa6, 0xbe, 0xcc, 0x87, 0x6e, 0xc0, 0x93, 0xcd, 0x88, 0x47, 0x7c, 0xd3, 0x1c, 0x1b,
	0xe6, 0x47, 0x26, 0x32, 0x81, 0x79, 0x2a, 0xca, 0xb5, 0x6f, 0x46, 0x9c, 0x47, 0x31, 0x4e, 0x58,
	0x98, 0x64, 0xea, 0xa4, 0x48, 0x3a, 0xdf, 0x59, 0x70, 0x6d, 0x9f, 0x8d, 0x58, 0x1a, 0x79, 0x9c,
	0x27, 0xfb, 0x4c, 0x2a, 0x0f, 0x9f, 0x92, 0x5d, 0x98, 0xcf, 0x68, 0x84, 0xb6, 0xd5, 0xb5, 0x36,
	0x6a, 0x0f, 0xef, 0xbe, 0x7c, 0xb1, 0x7e, 0xfb, 0x88, 0x8b, 0xe4, 0x81, 0xa3, 0x51, 0xa7, 0x3b,
	0xa2, 0x31, 0x0b, 0xa9, 0xc2, 0x07, 0x8e, 0xc0, 0xa7, 0x39, 0x13, 0x18, 0xde, 0x4b, 0x58, 0xba,
	0xbb, 0xed, 0x78, 0xe6, 0x18, 0x39, 0x84, 0x86, 0xfe, 0xf5, 0x25, 0xfb, 0x0a, 0xed, 0x39, 0x53,
	0x63, 0xe7, 0xe5, 0x8b, 0xf5, 0xde, 0xa4, 0x86, 0x49, 0x5d, 0x5c, 0xe8, 0x5e, 0x42, 0x8f, 0x77,
	0xb7, 0xb7, 0xb6, 0x1c, 0xaf, 0xae, 0xd9, 0x87, 0x9a, 0xfc, 0x83, 0x05, 0x6b, 0x1f, 0xc5, 0xf1,
	0x44, 0xac, 0xec, 0xa7, 0x47, 0xdc, 0x43, 0x99, 0x91, 0x47, 0x30, 0x1f, 0x33, 0xa9, 0x6c, 0xab,
	0x5b, 0xdb, 0x58, 0xea, 0x6d, 0xbb, 0x97, 0xf7, 0xcf, 0x7d, 0xa5, 0x8a, 0x29, 0x62, 0x8e, 0x93,
	0xcf, 0x00, 0x32, 0x1a, 0xb1, 0x94, 0x2a, 0xc6, 0x53, 0xa3, 0x7b, 0xa9, 0xf7, 0xde, 0xac, 0x62,
	0x07, 0xd5, 0x09, 0x6f, 0xea, 0xb4, 0x73, 0x04, 0x30, 0xc9, 0x10, 0x1b, 0x16, 0x13, 0x7a, 0x3c,
	0x69, 0xa9, 0x37, 0x0e, 0xc9, 0x75, 0xb8, 0x1a, 0xf0, 0x3c, 0x55, 0x45, 0x9b, 0xbc, 0x22, 0x20,
	0xcb, 0x60, 0x65, 0x76, 0xcd, 0x20, 0x56, 0x46, 0xda, 0x60, 0xba, 0x60, 0xba, 0x39, 0x6f, 0xc0,
	0x2a, 0x76, 0x7e, 0xab, 0xc1, 0xb5, 0x73, 0xff, 0x87, 0xac, 0xc1, 0x82, 0xe0, 0x3c, 0x61, 0x61,
	0x79, 0x5d, 0x19, 0x91, 0x55, 0xa8, 0xe5, 0x2c, 0x2c, 0xef, 0xd2, 0x8f, 0xe4, 0x2d, 0x80, 0x80,
	0x2a, 0x1a, 0xf3, 0xc8, 0x67, 0x61, 0x79, 0x65, 0xa3, 0x44, 0xfa, 0x21, 0x79, 0x07, 0x5a, 0x32,
	0x1f, 0xfa, 0x53, 0x94, 0x42, 0xc0, 0xb2, 0xcc, 0x87, 0x7b, 0x15, 0xcb, 0x81, 0x66, 0x90, 0x4b,
	0xc5, 0x13, 0x5f, 0x51, 0x43, 0xba, 0x6a, 0x48, 0x4b, 0x05, 0x38, 0xa0, 0x9a, 0xf3, 0x2e, 0xac,
	0x4c, 0x71, 0x52, 0x9a, 0xa0, 0xbd, 0xd0, 0xb5, 0x36, 0x1a, 0x5e, 0xb3, 0x62, 0x3d, 0xa1, 0x09,
	0x6a, 0x41, 0x5a, 0xac, 0xaf, 0x98, 0x8a, 0xd1, 0x5e, 0x34, 0x94, 0x86, 0x46, 0x06, 0x1a, 0x30,
	0xfd, 0x52, 0x2c, 0x41, 0xbb, 0x5e, 0xf6, 0x4b, 0x07, 0xba, 0x78, 0xcc, 0x46, 0xe8, 0x4b, 0x45,
	0x85, 0xf2, 0x4d, 0xbe, 0x61, 0xf2, 0x4d, 0x0d, 0x1f, 0x6a, 0x74, 0xa0, 0x79, 0x6b, 0xb0, 0xc0,
	0xd3, 0x98, 0xa5, 0x68, 0x43, 0xd1, 0x97, 0x22, 0x22, 0x77, 0x60, 0x85, 0x06, 0x2a, 0xa7, 0x71,
	0x7c, 0xe2, 0x97, 0x84, 0x25, 0x43, 0x68, 0x8d, 0xe1, 0xcf, 0x0b, 0xe2, 0xdb, 0xd0, 0x4c, 0x50,
	0x4a, 0xfd, 0x05, 0x17, 0xaf, 0x6d, 0xb9, 0x68, 0x47, 0x09, 0xee, 0x99, 0xb7, 0x77, 0x1d, 0xae,
	0xca, 0x80, 0x0b, 0xb4, 0x9b, 0x85, 0x46, 0x13, 0x68, 0x74, 0xc8, 0xa9, 0x08, 0xed, 0x56, 0x81,
	0x9a, 0x80, 0x74, 0x60, 0x89, 0x49, 0x3f, 0xc5, 0x67, 0x46, 0xbb, 0xbd, 0xd2, 0xb5, 0x36, 0xea,
	0x5e, 0x83, 0xc9, 0x27, 0xf8, 0x4c, 0xcb, 0x76, 0x7e, 0xb4, 0xe0, 0xd6, 0xa7, 0x3c, 0xc1, 0x4f,
	0x10, 0xc3, 0x7e, 0xaa, 0x50, 0x8c, 0x30, 0xd5, 0x9f, 0xd4, 0x1e, 0x15, 0xa1, 0xfc, 0xaf, 0x8e,
	0xaa, 0x16, 0xbd, 0xcf, 0x46, 0x78, 0x40, 0x23, 0xfc, 0xdf, 0x88, 0xfe, 0xde, 0x82, 0x1b, 0x7f,
	0x23, 0x56, 0x66, 0xe4, 0xe3, 0x57, 0xec, 0x65, 0x6b, 0x96, 0x23, 0x9c, 0x2d, 0xf2, 0x2f, 0xb8,
	0xcb, 0xcf, 0x73, 0xb0, 0x7a, 0xf6, 0x1a, 0xf2, 0x06, 0x2c, 0x06, 0x54, 0x84, 0xfe, 0x64, 0xea,
	0x75, 0xd8, 0x0f, 0xc9, 0x4d, 0x68, 0x98, 0x84, 0x3a, 0xc9, 0xca, 0x76, 0x79, 0x75, 0x0d, 0x0c,
	0x4e, 0x32, 0x33, 0x6f, 0x18, 0x63, 0x82, 0xa9, 0x9a, 0x32, 0x80, 0x12, 0xe9, 0x1b, 0x7f, 0x98,
	0x1a, 0xaa, 0x62, 0xf8, 0x1b, 0xb2, 0x1a, 0xa8, 0x37, 0xa1, 0x8e, 0x69, 0x58, 0x24, 0x8b, 0xa1,
	0x5f, 0xc4, 0x34, 0x34, 0xa9, 0x3b, 0xb0, 0x82, 0xc7, 0x19, 0x97, 0xb9, 0x40, 0x5f, 0x51, 0x11,
	0xa1, 0x32, 0x03, 0x5f, 0xf3, 0x5a, 0x63, 0x78, 0x60, 0x50, 0x72, 0x17, 0x56, 0x2b, 0x62, 0x90,
	0x0b, 0x81, 0xa9, 0x32, 0x73, 0x5f, 0xf3, 0xaa, 0x02, 0x7b, 0x05, 0x7c, 0xc6, 0xad, 0xea, 0x67,
	0xdd, 0xea, 0x36, 0xb4, 0x78, 0x86, 0x82, 0x2a, 0xf4, 0x25, 0xcf, 0x45, 0x50, 0xb9, 0x40, 0x89,
	0x1e, 0x1a, 0xb0, 0xf7, 0x8b, 0x05, 0x75, 0xfd, 0x79, 0x6a, 0xbb, 0x24, 0x3b, 0x30, 0x7f, 0xc0,
	0xd2, 0x88, 0xac, 0xb9, 0xc5, 0x9a, 0x74, 0xc7, 0x6b, 0xd2, 0x7d, 0xa4, 0xd7, 0x64, 0xfb, 0x02,
	0x9c, 0x7c, 0x6d, 0xc1, 0x8d, 0xc7, 0xa8, 0xce, 0x6f, 0x24, 0x32, 0x73, 0xff, 0x9c, 0xdb, 0xb7,
	0xed, 0x9d, 0xd7, 0x5a, 0x59, 0xd5, 0xe2, 0xeb, 0xfd, 0x34, 0x07, 0x0d, 0x6f, 0xcc, 0x25, 0xdf,
	0x58, 0x70, 0xeb, 0x31, 0xaa, 0x0b, 0xed, 0x82, 0x7c, 0x38, 0xeb, 0x9a, 0xcb, 0x9c, 0xa6, 0xfd,
	0xc1, 0xeb, 0x7e, 0xf8, 0xc5, 0xf4, 0x94, 0xaa, 0x2e, 0xf4, 0x83, 0xd9, 0xaa, 0x2e, 0xb3, 0x92,
	0x7f, 0xa8, 0xea, 0xa1, 0xfd, 0xfc, 0x8f, 0xce, 0x95, 0xe7, 0xa7, 0x1d, 0xeb, 0xd7, 0xd3, 0x8e,
	0xf5, 0xfb, 0x69, 0xc7, 0xfa, 0xf6, 0xcf, 0xce, 0x95, 0x2f, 0xe6, 0x46, 0xbd, 0xe1, 0x82, 0x79,
	0xd1, 0xef, 0xff, 0x15, 0x00, 0x00, 0xff, 0xff, 0xc9, 0xe9, 0xe4, 0x9b, 0xa7, 0x09, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// LiveRoomClient is the client API for LiveRoom service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LiveRoomClient interface {
	Ping(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*empty.Empty, error)
	GetAllLivingRoomsInfo(ctx context.Context, in *LivingRoomListReq, opts ...grpc.CallOption) (*AllLivingRoomsInfoResp, error)
}

type liveRoomClient struct {
	cc *grpc.ClientConn
}

func NewLiveRoomClient(cc *grpc.ClientConn) LiveRoomClient {
	return &liveRoomClient{cc}
}

func (c *liveRoomClient) Ping(ctx context.Context, in *empty.Empty, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/maoer.service.recommendbase.v2.LiveRoom/Ping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *liveRoomClient) GetAllLivingRoomsInfo(ctx context.Context, in *LivingRoomListReq, opts ...grpc.CallOption) (*AllLivingRoomsInfoResp, error) {
	out := new(AllLivingRoomsInfoResp)
	err := c.cc.Invoke(ctx, "/maoer.service.recommendbase.v2.LiveRoom/GetAllLivingRoomsInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LiveRoomServer is the server API for LiveRoom service.
type LiveRoomServer interface {
	Ping(context.Context, *empty.Empty) (*empty.Empty, error)
	GetAllLivingRoomsInfo(context.Context, *LivingRoomListReq) (*AllLivingRoomsInfoResp, error)
}

// UnimplementedLiveRoomServer can be embedded to have forward compatible implementations.
type UnimplementedLiveRoomServer struct {
}

func (*UnimplementedLiveRoomServer) Ping(ctx context.Context, req *empty.Empty) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (*UnimplementedLiveRoomServer) GetAllLivingRoomsInfo(ctx context.Context, req *LivingRoomListReq) (*AllLivingRoomsInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllLivingRoomsInfo not implemented")
}

func RegisterLiveRoomServer(s *grpc.Server, srv LiveRoomServer) {
	s.RegisterService(&_LiveRoom_serviceDesc, srv)
}

func _LiveRoom_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(empty.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiveRoomServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maoer.service.recommendbase.v2.LiveRoom/Ping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiveRoomServer).Ping(ctx, req.(*empty.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _LiveRoom_GetAllLivingRoomsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LivingRoomListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LiveRoomServer).GetAllLivingRoomsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maoer.service.recommendbase.v2.LiveRoom/GetAllLivingRoomsInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LiveRoomServer).GetAllLivingRoomsInfo(ctx, req.(*LivingRoomListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _LiveRoom_serviceDesc = grpc.ServiceDesc{
	ServiceName: "maoer.service.recommendbase.v2.LiveRoom",
	HandlerType: (*LiveRoomServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _LiveRoom_Ping_Handler,
		},
		{
			MethodName: "GetAllLivingRoomsInfo",
			Handler:    _LiveRoom_GetAllLivingRoomsInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "recommend-base/api/v2/api.proto",
}

// RecommendClient is the client API for Recommend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecommendClient interface {
	// 获取首页 Feed 干预卡列表
	GetHomeFeedInterventionCards(ctx context.Context, in *HomeFeedInterventionCardsReq, opts ...grpc.CallOption) (*InterventionCardsResp, error)
	// 获取直播页干预卡列表
	GetLivePageInterventionCards(ctx context.Context, in *LivePageInterventionCardsReq, opts ...grpc.CallOption) (*InterventionCardsResp, error)
}

type recommendClient struct {
	cc *grpc.ClientConn
}

func NewRecommendClient(cc *grpc.ClientConn) RecommendClient {
	return &recommendClient{cc}
}

func (c *recommendClient) GetHomeFeedInterventionCards(ctx context.Context, in *HomeFeedInterventionCardsReq, opts ...grpc.CallOption) (*InterventionCardsResp, error) {
	out := new(InterventionCardsResp)
	err := c.cc.Invoke(ctx, "/maoer.service.recommendbase.v2.Recommend/GetHomeFeedInterventionCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recommendClient) GetLivePageInterventionCards(ctx context.Context, in *LivePageInterventionCardsReq, opts ...grpc.CallOption) (*InterventionCardsResp, error) {
	out := new(InterventionCardsResp)
	err := c.cc.Invoke(ctx, "/maoer.service.recommendbase.v2.Recommend/GetLivePageInterventionCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecommendServer is the server API for Recommend service.
type RecommendServer interface {
	// 获取首页 Feed 干预卡列表
	GetHomeFeedInterventionCards(context.Context, *HomeFeedInterventionCardsReq) (*InterventionCardsResp, error)
	// 获取直播页干预卡列表
	GetLivePageInterventionCards(context.Context, *LivePageInterventionCardsReq) (*InterventionCardsResp, error)
}

// UnimplementedRecommendServer can be embedded to have forward compatible implementations.
type UnimplementedRecommendServer struct {
}

func (*UnimplementedRecommendServer) GetHomeFeedInterventionCards(ctx context.Context, req *HomeFeedInterventionCardsReq) (*InterventionCardsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHomeFeedInterventionCards not implemented")
}
func (*UnimplementedRecommendServer) GetLivePageInterventionCards(ctx context.Context, req *LivePageInterventionCardsReq) (*InterventionCardsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLivePageInterventionCards not implemented")
}

func RegisterRecommendServer(s *grpc.Server, srv RecommendServer) {
	s.RegisterService(&_Recommend_serviceDesc, srv)
}

func _Recommend_GetHomeFeedInterventionCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HomeFeedInterventionCardsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecommendServer).GetHomeFeedInterventionCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maoer.service.recommendbase.v2.Recommend/GetHomeFeedInterventionCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecommendServer).GetHomeFeedInterventionCards(ctx, req.(*HomeFeedInterventionCardsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Recommend_GetLivePageInterventionCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LivePageInterventionCardsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecommendServer).GetLivePageInterventionCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maoer.service.recommendbase.v2.Recommend/GetLivePageInterventionCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecommendServer).GetLivePageInterventionCards(ctx, req.(*LivePageInterventionCardsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Recommend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "maoer.service.recommendbase.v2.Recommend",
	HandlerType: (*RecommendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHomeFeedInterventionCards",
			Handler:    _Recommend_GetHomeFeedInterventionCards_Handler,
		},
		{
			MethodName: "GetLivePageInterventionCards",
			Handler:    _Recommend_GetLivePageInterventionCards_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "recommend-base/api/v2/api.proto",
}

func (m *LivingRoomListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LivingRoomListReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LivingRoomListReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PageSize != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.PageSize))
		i--
		dAtA[i] = 0x10
	}
	if m.Page != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Page))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AllLivingRoomsInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllLivingRoomsInfoResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllLivingRoomsInfoResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Pagination != nil {
		{
			size, err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.MarshalToSizedBuffer(m.Pagination, dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintApi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.List) > 0 {
		for iNdEx := len(m.List) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.MarshalToSizedBuffer(m.List[iNdEx], dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintApi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Pagination) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Pagination) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Pagination) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Pagesize != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Pagesize))
		i--
		dAtA[i] = 0x20
	}
	if m.P != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.P))
		i--
		dAtA[i] = 0x18
	}
	if m.Count != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x10
	}
	if m.Maxpage != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Maxpage))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AllLivingRoomInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllLivingRoomInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AllLivingRoomInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.IsNewStar {
		i--
		if m.IsNewStar {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x78
	}
	if m.Board != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Board))
		i--
		dAtA[i] = 0x70
	}
	if m.Score != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Score))
		i--
		dAtA[i] = 0x68
	}
	if m.MessageCount != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.MessageCount))
		i--
		dAtA[i] = 0x60
	}
	if m.ActuallyOnline != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.ActuallyOnline))
		i--
		dAtA[i] = 0x58
	}
	if m.Online != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Online))
		i--
		dAtA[i] = 0x50
	}
	if m.LiveStartTime != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.LiveStartTime))
		i--
		dAtA[i] = 0x48
	}
	if m.Ctime != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Ctime))
		i--
		dAtA[i] = 0x40
	}
	if len(m.RoomTitle) > 0 {
		i -= len(m.RoomTitle)
		copy(dAtA[i:], m.RoomTitle)
		i = encodeVarintApi(dAtA, i, uint64(len(m.RoomTitle)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.CustomTagName) > 0 {
		i -= len(m.CustomTagName)
		copy(dAtA[i:], m.CustomTagName)
		i = encodeVarintApi(dAtA, i, uint64(len(m.CustomTagName)))
		i--
		dAtA[i] = 0x32
	}
	if m.CustomTagId != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.CustomTagId))
		i--
		dAtA[i] = 0x28
	}
	if m.SubCatalogId != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.SubCatalogId))
		i--
		dAtA[i] = 0x20
	}
	if m.CatalogId != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.CatalogId))
		i--
		dAtA[i] = 0x18
	}
	if m.Uid != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Uid))
		i--
		dAtA[i] = 0x10
	}
	if m.Roomid != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Roomid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *HomeFeedInterventionCardsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HomeFeedInterventionCardsReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *HomeFeedInterventionCardsReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PageSize != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.PageSize))
		i--
		dAtA[i] = 0x10
	}
	if m.Page != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Page))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LivePageInterventionCardsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LivePageInterventionCardsReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LivePageInterventionCardsReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PageSize != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.PageSize))
		i--
		dAtA[i] = 0x10
	}
	if m.Page != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.Page))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *InterventionCardsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InterventionCardsResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InterventionCardsResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Pagination != nil {
		{
			size, err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.MarshalToSizedBuffer(m.Pagination, dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintApi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.List) > 0 {
		for iNdEx := len(m.List) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.MarshalToSizedBuffer(m.List[iNdEx], dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintApi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *InterventionCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InterventionCard) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InterventionCard) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.OperateSource != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.OperateSource))
		i--
		dAtA[i] = 0x48
	}
	if m.CatalogId != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.CatalogId))
		i--
		dAtA[i] = 0x40
	}
	if m.ExposureCurrent != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.ExposureCurrent))
		i--
		dAtA[i] = 0x38
	}
	if m.ExposureTarget != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.ExposureTarget))
		i--
		dAtA[i] = 0x30
	}
	if m.EndTime != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.EndTime))
		i--
		dAtA[i] = 0x28
	}
	if m.StartTime != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.StartTime))
		i--
		dAtA[i] = 0x20
	}
	if m.ElementId != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.ElementId))
		i--
		dAtA[i] = 0x18
	}
	if m.CardType != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.CardType))
		i--
		dAtA[i] = 0x10
	}
	if m.CardId != 0 {
		i = encodeVarintApi(dAtA, i, uint64(m.CardId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintApi(dAtA []byte, offset int, v uint64) int {
	offset -= sovApi(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *LivingRoomListReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Page != 0 {
		n += 1 + sovApi(uint64(m.Page))
	}
	if m.PageSize != 0 {
		n += 1 + sovApi(uint64(m.PageSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AllLivingRoomsInfoResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = git_bilibili_co_go_kratos_gogo_protobuf_compatible.Size(e)
			n += 1 + l + sovApi(uint64(l))
		}
	}
	if m.Pagination != nil {
		l = git_bilibili_co_go_kratos_gogo_protobuf_compatible.Size(m.Pagination)
		n += 1 + l + sovApi(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Pagination) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Maxpage != 0 {
		n += 1 + sovApi(uint64(m.Maxpage))
	}
	if m.Count != 0 {
		n += 1 + sovApi(uint64(m.Count))
	}
	if m.P != 0 {
		n += 1 + sovApi(uint64(m.P))
	}
	if m.Pagesize != 0 {
		n += 1 + sovApi(uint64(m.Pagesize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AllLivingRoomInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Roomid != 0 {
		n += 1 + sovApi(uint64(m.Roomid))
	}
	if m.Uid != 0 {
		n += 1 + sovApi(uint64(m.Uid))
	}
	if m.CatalogId != 0 {
		n += 1 + sovApi(uint64(m.CatalogId))
	}
	if m.SubCatalogId != 0 {
		n += 1 + sovApi(uint64(m.SubCatalogId))
	}
	if m.CustomTagId != 0 {
		n += 1 + sovApi(uint64(m.CustomTagId))
	}
	l = len(m.CustomTagName)
	if l > 0 {
		n += 1 + l + sovApi(uint64(l))
	}
	l = len(m.RoomTitle)
	if l > 0 {
		n += 1 + l + sovApi(uint64(l))
	}
	if m.Ctime != 0 {
		n += 1 + sovApi(uint64(m.Ctime))
	}
	if m.LiveStartTime != 0 {
		n += 1 + sovApi(uint64(m.LiveStartTime))
	}
	if m.Online != 0 {
		n += 1 + sovApi(uint64(m.Online))
	}
	if m.ActuallyOnline != 0 {
		n += 1 + sovApi(uint64(m.ActuallyOnline))
	}
	if m.MessageCount != 0 {
		n += 1 + sovApi(uint64(m.MessageCount))
	}
	if m.Score != 0 {
		n += 1 + sovApi(uint64(m.Score))
	}
	if m.Board != 0 {
		n += 1 + sovApi(uint64(m.Board))
	}
	if m.IsNewStar {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *HomeFeedInterventionCardsReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Page != 0 {
		n += 1 + sovApi(uint64(m.Page))
	}
	if m.PageSize != 0 {
		n += 1 + sovApi(uint64(m.PageSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *LivePageInterventionCardsReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Page != 0 {
		n += 1 + sovApi(uint64(m.Page))
	}
	if m.PageSize != 0 {
		n += 1 + sovApi(uint64(m.PageSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *InterventionCardsResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = git_bilibili_co_go_kratos_gogo_protobuf_compatible.Size(e)
			n += 1 + l + sovApi(uint64(l))
		}
	}
	if m.Pagination != nil {
		l = git_bilibili_co_go_kratos_gogo_protobuf_compatible.Size(m.Pagination)
		n += 1 + l + sovApi(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *InterventionCard) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CardId != 0 {
		n += 1 + sovApi(uint64(m.CardId))
	}
	if m.CardType != 0 {
		n += 1 + sovApi(uint64(m.CardType))
	}
	if m.ElementId != 0 {
		n += 1 + sovApi(uint64(m.ElementId))
	}
	if m.StartTime != 0 {
		n += 1 + sovApi(uint64(m.StartTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovApi(uint64(m.EndTime))
	}
	if m.ExposureTarget != 0 {
		n += 1 + sovApi(uint64(m.ExposureTarget))
	}
	if m.ExposureCurrent != 0 {
		n += 1 + sovApi(uint64(m.ExposureCurrent))
	}
	if m.CatalogId != 0 {
		n += 1 + sovApi(uint64(m.CatalogId))
	}
	if m.OperateSource != 0 {
		n += 1 + sovApi(uint64(m.OperateSource))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovApi(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozApi(x uint64) (n int) {
	return sovApi(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *LivingRoomListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LivingRoomListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LivingRoomListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Page", wireType)
			}
			m.Page = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Page |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			m.PageSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllLivingRoomsInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllLivingRoomsInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllLivingRoomsInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.List = append(m.List, &AllLivingRoomInfo{})
			if err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.Unmarshal(m.List[len(m.List)-1], dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pagination", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Pagination == nil {
				m.Pagination = &Pagination{}
			}
			if err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.Unmarshal(m.Pagination, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Pagination) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Pagination: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Pagination: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Maxpage", wireType)
			}
			m.Maxpage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Maxpage |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field P", wireType)
			}
			m.P = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.P |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pagesize", wireType)
			}
			m.Pagesize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Pagesize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllLivingRoomInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllLivingRoomInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllLivingRoomInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Roomid", wireType)
			}
			m.Roomid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Roomid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CatalogId", wireType)
			}
			m.CatalogId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CatalogId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SubCatalogId", wireType)
			}
			m.SubCatalogId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubCatalogId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CustomTagId", wireType)
			}
			m.CustomTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CustomTagId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CustomTagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CustomTagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RoomTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RoomTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ctime", wireType)
			}
			m.Ctime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ctime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LiveStartTime", wireType)
			}
			m.LiveStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LiveStartTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Online", wireType)
			}
			m.Online = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Online |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActuallyOnline", wireType)
			}
			m.ActuallyOnline = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActuallyOnline |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MessageCount", wireType)
			}
			m.MessageCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MessageCount |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Board", wireType)
			}
			m.Board = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Board |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsNewStar", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNewStar = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *HomeFeedInterventionCardsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: HomeFeedInterventionCardsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: HomeFeedInterventionCardsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Page", wireType)
			}
			m.Page = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Page |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			m.PageSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LivePageInterventionCardsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LivePageInterventionCardsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LivePageInterventionCardsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Page", wireType)
			}
			m.Page = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Page |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			m.PageSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InterventionCardsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InterventionCardsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InterventionCardsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.List = append(m.List, &InterventionCard{})
			if err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.Unmarshal(m.List[len(m.List)-1], dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pagination", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Pagination == nil {
				m.Pagination = &Pagination{}
			}
			if err := git_bilibili_co_go_kratos_gogo_protobuf_compatible.Unmarshal(m.Pagination, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InterventionCard) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InterventionCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InterventionCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CardId", wireType)
			}
			m.CardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ElementId", wireType)
			}
			m.ElementId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ElementId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExposureTarget", wireType)
			}
			m.ExposureTarget = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExposureTarget |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExposureCurrent", wireType)
			}
			m.ExposureCurrent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExposureCurrent |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CatalogId", wireType)
			}
			m.CatalogId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CatalogId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OperateSource", wireType)
			}
			m.OperateSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperateSource |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipApi(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowApi
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowApi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowApi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthApi
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupApi
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthApi
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthApi        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowApi          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupApi = fmt.Errorf("proto: unexpected end of group")
)
